import { useState } from 'react'
import ReescheduleAppointment from './RescheduleCalendar'
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { actions } from 'astro:actions';
import toast from 'react-hot-toast';


interface StepsProps {
  data: any;
}

export default function Steps({ data: appointmentData }: StepsProps) {

  const [step, setStep] = useState(0);
  // const [appointmentData, setAppointmentData] = useState<any>(data);

  const steps = [
    () => <AskForPlates step={step} setStep={setStep} appointmentData={appointmentData} />,
    () => <ReescheduleAppointment appointmentData={appointmentData} />,
  ]

  return (
    <>
      <div className="fixed inset-0 flex items-center justify-center bg-gray-200 w-full ">
        {/* <div className="bg-white p-4 rounded-lg shadow-lg space-y-4 flex w-full "> */}
        <div className="bg-white p-4 rounded-lg shadow-lg ">
          {steps[step]()}
        </div>
      </div>
    </>
  )
}

interface AskForPlatesProps {
  step: number;
  setStep: (step: number) => void;
  appointmentData: any;
}

function AskForPlates({ step, setStep, appointmentData }: AskForPlatesProps) {

  const [plates, setPlates] = useState('');

  return (
    <div className='w-full xs:min-w-[300px]  lg:w-[500px] space-y-4'>


      <h2 className="text-lg font-semibold">Ingresa tu placa</h2>
      <Input
        type="text"
        value={plates}
        onChange={(e) => {
          setPlates(e.target.value.toUpperCase().replace(/[^A-Z0-9]/g, ''));
        }}
      />

      <Button
        className='bg-purple-800/90 hover:bg-purple-900 text-white '
        onClick={async () => {
          console.log('appointmentData', appointmentData);
          console.log(plates, appointmentData.stock.carPlates.plates);
          if (plates === appointmentData.stock.carPlates.plates.toUpperCase()) {
            setStep(step + 1);
          } else {
            toast.error('Placas incorrectas');
          }
        }}
      >
        Confirmar
      </Button>

    </div>
  )
}