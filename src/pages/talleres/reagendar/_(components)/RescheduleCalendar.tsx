"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft, ChevronLeft, ChevronRight, LoaderCircle } from "lucide-react"
import { useReschedule } from "./RescheduleContext"
import { actions } from 'astro:actions'
import toast from "react-hot-toast"

interface DayInfo {
  date: Date | null;
  disabled: boolean;
  isToday: boolean;
}

export default function RescheduleCalendar() {
  const {
    appointmentInfo,
    setCurrentStep,
    selectedDate, 
    setSelectedDate,
    setAvailableSlots,
    setLoading,
    loading,
    setError
  } = useReschedule()

  const [currentMonth, setCurrentMonth] = useState<Date>(new Date())

  // Reiniciar el error al montar el componente
  useEffect(() => {
    setError(null)
  }, [setError])

  if (!appointmentInfo?.appointment) {
    return null
  }

  const { workshop, serviceType } = appointmentInfo.appointment

  const goBack = () => {
    setCurrentStep(1)
  }

  const DAYS = ["<PERSON>", "Lu<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>á<PERSON>"]

  const goToPreviousMonth = () => {
    setCurrentMonth(prevMonth => {
      const newMonth = new Date(prevMonth)
      newMonth.setMonth(newMonth.getMonth() - 1)
      return newMonth
    })
  }

  const goToNextMonth = () => {
    setCurrentMonth(prevMonth => {
      const newMonth = new Date(prevMonth)
      newMonth.setMonth(newMonth.getMonth() + 1)
      return newMonth
    })
  }

  const formatMonthYear = (date: Date) => {
    return date.toLocaleDateString('es-ES', { 
      month: 'long', 
      year: 'numeric' 
    })
  }

  const generateCalendarDays = (): DayInfo[] => {
    const year = currentMonth.getFullYear()
    const month = currentMonth.getMonth()
    
    const firstDay = new Date(year, month, 1)
    const lastDay = new Date(year, month + 1, 0)
    const startDate = new Date(firstDay)
    startDate.setDate(startDate.getDate() - firstDay.getDay())
    
    const days: DayInfo[] = []
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    // Obtener la configuración de horarios del taller
    const weeklySchedule = workshop.scheduleConfig.weeklySchedule
    
    // Mapeo de días de la semana
    const dayMap: { [key: number]: string } = {
      0: 'sunday',
      1: 'monday', 
      2: 'tuesday',
      3: 'wednesday',
      4: 'thursday',
      5: 'friday',
      6: 'saturday'
    }

    // Generar días del mes anterior para completar la primera semana
    for (let i = 0; i < firstDay.getDay(); i++) {
      const date = new Date(startDate)
      date.setDate(date.getDate() + i)
      days.push({
        date: null,
        disabled: true,
        isToday: false,
      })
    }

    // Generar días del mes actual
    for (let i = 1; i <= lastDay.getDate(); i++) {
      const currentDate = new Date(year, month, i)
      const dayOfWeek = currentDate.getDay()
      const dayName = dayMap[dayOfWeek]

      // Verificar si el día está en el horario del taller
      const isDayAvailable = weeklySchedule[dayName] !== undefined

      // Verificar si el día es hoy o futuro
      const isPastDay = currentDate < today

      days.push({
        date: currentDate,
        disabled: isPastDay || !isDayAvailable,
        isToday: currentDate.toDateString() === today.toDateString(),
      })
    }

    return days
  }

  const handleDateSelect = async (dayInfo: DayInfo) => {
    if (!dayInfo.date || dayInfo.disabled) return

    // Inmediatamente establecer la fecha seleccionada y avanzar al siguiente paso
    setSelectedDate(dayInfo.date)
    setCurrentStep(3)

    try {
      setLoading(true)
      setError(null)

      const dateStr = dayInfo.date.toISOString().split('T')[0]

      // Obtener los slots disponibles usando la acción correcta
      const result = await actions.getAvailableWorkshopSlots({
        workshopId: workshop._id,
        date: dateStr,
        serviceTypeId: serviceType._id,
      })

      if (!result.data?.success || !result.data) {
        toast.error(result.data?.error?.message || "No se pudieron obtener los horarios disponibles")
        setError(result.data?.error?.message || "No se pudieron obtener los horarios disponibles")
        // Si hay un error, volver al calendario
        setCurrentStep(2)
        return
      }

      setAvailableSlots(result.data.data || [])

    } catch (error: any) {
      console.error("Error al obtener slots:", error)
      toast.error("Error al obtener horarios disponibles")
      setError("Error al obtener horarios disponibles")
      // Si hay un error, volver al calendario
      setCurrentStep(2)
    } finally {
      setLoading(false)
    }
  }

  const calendarDays = generateCalendarDays()

  return (
    <div className="w-full space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Button variant="ghost" size="sm" onClick={goBack}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h2 className="text-lg font-semibold">Seleccionar fecha</h2>
        <div className="w-8" /> {/* Spacer */}
      </div>

      {/* Información del taller */}
      <div className="text-center space-y-2">
        <h3 className="font-medium text-gray-900">{workshop.name}</h3>
        <p className="text-sm text-gray-600">{serviceType.name}</p>
      </div>

      {/* Navegación del mes */}
      <div className="flex items-center justify-between">
        <Button variant="ghost" size="sm" onClick={goToPreviousMonth}>
          <ChevronLeft className="h-4 w-4" />
        </Button>
        <h3 className="text-lg font-medium capitalize">
          {formatMonthYear(currentMonth)}
        </h3>
        <Button variant="ghost" size="sm" onClick={goToNextMonth}>
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>

      {/* Calendario */}
      <div className="space-y-2">
        {/* Días de la semana */}
        <div className="grid grid-cols-7 gap-1">
          {DAYS.map((day) => (
            <div key={day} className="text-center text-sm font-medium text-gray-500 py-2">
              {day}
            </div>
          ))}
        </div>

        {/* Días del mes */}
        <div className="grid grid-cols-7 gap-1">
          {calendarDays.map((dayInfo, index) => (
            <Button
              key={index}
              variant={dayInfo.date && selectedDate && dayInfo.date.toDateString() === selectedDate.toDateString() ? "default" : "ghost"}
              size="sm"
              className={`
                h-10 w-full p-0 text-sm
                ${dayInfo.disabled 
                  ? "text-gray-300 cursor-not-allowed hover:bg-transparent" 
                  : "hover:bg-violet-50 hover:text-violet-700"
                }
                ${dayInfo.isToday ? "bg-violet-100 text-violet-700 font-semibold" : ""}
                ${dayInfo.date && selectedDate && dayInfo.date.toDateString() === selectedDate.toDateString() 
                  ? "bg-violet-700 text-white hover:bg-violet-800" 
                  : ""
                }
              `}
              onClick={() => handleDateSelect(dayInfo)}
              disabled={dayInfo.disabled || loading}
            >
              {dayInfo.date ? dayInfo.date.getDate() : ""}
            </Button>
          ))}
        </div>
      </div>

      {loading && (
        <div className="flex flex-col items-center justify-center py-4">
          <LoaderCircle className="h-6 w-6 text-violet-700 animate-spin mb-2" />
          <p className="text-sm text-gray-600">Cargando horarios disponibles...</p>
        </div>
      )}
    </div>
  )
}
