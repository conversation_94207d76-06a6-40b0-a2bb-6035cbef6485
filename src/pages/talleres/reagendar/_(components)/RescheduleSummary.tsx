"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardHeader, CardTitle, CardContent } from "@/components/ui/card"
import { ArrowLeft, Calendar, Clock, MapPin, Car, LoaderCircle } from "lucide-react"
import { useReschedule } from "./RescheduleContext"
import { DateTime } from "luxon"
import { actions } from "astro:actions"
import toast from "react-hot-toast"

export default function RescheduleSummary() {
  const {
    appointmentInfo,
    selectedDate,
    selectedSlot,
    setCurrentStep,
    setLoading,
    loading
  } = useReschedule()

  const [isConfirming, setIsConfirming] = useState(false)

  if (!appointmentInfo?.appointment || !selectedDate || !selectedSlot) {
    return null
  }

  const { appointment } = appointmentInfo
  const { workshop, serviceType, associate, stockVehicle } = appointment

  const goBack = () => {
    setCurrentStep(3)
  }

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('es-ES', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const formatTimeRange = (startTime: string) => {
    const start = DateTime.fromISO(startTime);
    const end = start.plus({ minutes: serviceType.duration });

    const startFormatted = start.toFormat('hh:mm a').toLowerCase();
    const endFormatted = end.toFormat('hh:mm a').toLowerCase();

    return `${startFormatted} - ${endFormatted}`;
  };

  const handleConfirmReschedule = async () => {
    setIsConfirming(true)
    setLoading(true)

    try {
      const result = await actions.rescheduleAppointment({
        appointmentId: appointment._id,
        startTime: selectedSlot,
        serviceTypeId: serviceType._id
      })

      if (!result.data?.success) {
        toast.error(result.data?.error?.message || "Error al reagendar la cita")
        return
      }

      toast.success("Cita reagendada exitosamente")
      setCurrentStep(5)

    } catch (error: any) {
      console.error("Error al reagendar:", error)
      toast.error("Error al reagendar la cita")
    } finally {
      setIsConfirming(false)
      setLoading(false)
    }
  }

  return (
    <div className="w-full space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Button variant="ghost" size="sm" onClick={goBack} disabled={isConfirming}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h2 className="text-lg font-semibold">Confirmar reagendamiento</h2>
        <div className="w-8" /> {/* Spacer */}
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-center text-xl">Resumen de la cita</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6 text-start">
          {/* Información del vehículo */}
          <div className="flex items-start gap-3">
            <Car className="h-5 w-5 text-gray-500 mt-0.5" />
            <div>
              <p className="font-medium text-gray-900">
                {stockVehicle.brand} {stockVehicle.model} {stockVehicle.version}
              </p>
              <p className="text-sm text-gray-600">
                Placas: {stockVehicle.carPlates.plates}
              </p>
              <p className="text-sm text-gray-600">
                Año: {stockVehicle.year}
              </p>
            </div>
          </div>

          {/* Información del taller */}
          <div className="flex items-start gap-3">
            <MapPin className="h-5 w-5 text-gray-500 mt-0.5" />
            <div>
              <p className="font-medium text-gray-900">{workshop.name}</p>
              <p className="text-sm text-gray-600">{serviceType.name}</p>
            </div>
          </div>

          {/* Nueva fecha y hora */}
          <div className="bg-violet-50 border border-violet-200 rounded-lg p-4">
            <h4 className="font-medium text-violet-900 mb-3">Nueva fecha y hora:</h4>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-violet-600" />
                <span className="text-sm text-violet-800 capitalize">
                  {formatDate(selectedDate)}
                </span>
              </div>

              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-violet-600" />
                <span className="text-sm text-violet-800">
                  {formatTimeRange(selectedSlot)}
                </span>
              </div>
            </div>
          </div>

          {/* Información anterior */}
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <h4 className="font-medium text-gray-700 mb-3">Fecha anterior:</h4>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-gray-500" />
                <span className="text-sm text-gray-600 capitalize line-through">
                  {DateTime.fromISO(appointment.startTime).toFormat('cccc, dd \'de\' LLLL \'de\' yyyy')}
                </span>
              </div>

              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-gray-500" />
                <span className="text-sm text-gray-600 line-through">
                  {formatTimeRange(appointment.startTime)}
                </span>
              </div>
            </div>
          </div>

          {/* Botón de confirmación */}
          <Button
            onClick={handleConfirmReschedule}
            className="w-full bg-violet-700 hover:bg-violet-800"
            disabled={isConfirming}
          >
            {isConfirming ? (
              <>
                <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
                Reagendando...
              </>
            ) : (
              "Confirmar reagendamiento"
            )}
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}
