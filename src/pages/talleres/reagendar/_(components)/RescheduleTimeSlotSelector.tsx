"use client"

import { useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft, Clock, LoaderCircle } from "lucide-react"
import { useReschedule } from "./RescheduleContext"
import { DateTime } from "luxon"

export default function RescheduleTimeSlotSelector() {
  const { 
    availableSlots, 
    selectedSlot, 
    setSelectedSlot, 
    setCurrentStep, 
    selectedDate,
    loading,
    appointmentInfo,
    setError
  } = useReschedule()

  // Reiniciar el error al montar el componente
  useEffect(() => {
    setError(null)
  }, [setError])

  if (!appointmentInfo?.appointment) {
    return null
  }

  const { serviceType } = appointmentInfo.appointment

  const goBack = () => {
    setCurrentStep(2)
  }

  const formatTimeRange = (startTime: string) => {
    const start = DateTime.fromISO(startTime);
    const end = start.plus({ minutes: serviceType?.duration || 60 });

    const startFormatted = start.toFormat('hh:mm a').toLowerCase();
    const endFormatted = end.toFormat('hh:mm a').toLowerCase();

    return `${startFormatted} - ${endFormatted}`;
  };

  const formatSelectedDate = () => {
    if (!selectedDate) return ""
    return selectedDate.toLocaleDateString('es-ES', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  return (
    <div className="w-full space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Button variant="ghost" size="sm" onClick={goBack}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h2 className="text-lg font-semibold">Seleccionar horario</h2>
        <div className="w-8" /> {/* Spacer */}
      </div>

      {/* Información de la fecha seleccionada */}
      <div className="text-center space-y-2">
        <p className="text-sm text-gray-600">Fecha seleccionada:</p>
        <p className="font-medium text-gray-900 capitalize">{formatSelectedDate()}</p>
      </div>
      
      {loading ? (
        <div className="flex flex-col items-center justify-center py-10">
          <LoaderCircle className="h-10 w-10 text-violet-700 animate-spin mb-4" />
          <p className="text-gray-600">Cargando horarios disponibles...</p>
        </div>
      ) : (
        <>
          {availableSlots.length === 0 ? (
            <div className="text-center p-4">
              <p>No hay horarios disponibles para la fecha seleccionada.</p>
              <Button 
                  variant="outline"
                  onClick={() => setCurrentStep(2)}
                  className="mt-4"
                >
                  Seleccionar otra fecha
                </Button>
            </div>
            ) : (
              <div className="flex flex-col gap-3 mx-auto items-center">
                {availableSlots.map((slot) => (
                  <Button
                    key={slot}
                    variant="outline"
                    onClick={() => {
                      setSelectedSlot(slot);
                      setCurrentStep(4);
                    }}
                    className={`
                      p-4 flex items-center justify-between w-full max-w-xs
                      ${selectedSlot === slot 
                        ? "bg-violet-700 text-white border-violet-700 hover:bg-violet-800" 
                        : "hover:bg-violet-50 hover:border-violet-300"
                      }
                    `}
                  >
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4" />
                      <span className="font-medium">
                        {formatTimeRange(slot)}
                      </span>
                    </div>
                  </Button>
                ))}
              </div>
            )}
        </>
      )}
    </div>
  )
}
