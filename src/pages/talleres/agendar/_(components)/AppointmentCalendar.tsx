"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft, ChevronLeft, ChevronRight } from "lucide-react"
import { useAppointment } from "./AppointmentContext"
import { actions } from 'astro:actions'
import toast from "react-hot-toast"

interface DayInfo {
  date: Date | null;
  disabled: boolean;
  isToday: boolean;
}

export default function AppointmentCalendar({ setError }: { setError: (error: string | null) => void }) {
  const {
    selectedWorkshop, 
    selectedServiceType,
    setCurrentStep,
    selectedDate, 
    setSelectedDate,
    setAvailableSlots,
    setLoading,
    loading
  } = useAppointment()

  const [currentMonth, setCurrentMonth] = useState<Date>(new Date())

  // Reiniciar el error al montar el componente
  useEffect(() => {
    setError(null)
  }, [setError])

  if (!selectedWorkshop || !selectedServiceType) {
    return null
  }

  const goBack = () => {
    setCurrentStep(2)
  }

  const DAYS = ["<PERSON>", "Lun", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Vie", "<PERSON>áb"]

  const goToPreviousMonth = () => {
    setCurrentMonth(prevMonth => {
      const newMonth = new Date(prevMonth)
      newMonth.setMonth(newMonth.getMonth() - 1)
      return newMonth
    })
  }

  const goToNextMonth = () => {
    setCurrentMonth(prevMonth => {
      const newMonth = new Date(prevMonth)
      newMonth.setMonth(newMonth.getMonth() + 1)
      return newMonth
    })
  }

  const getDaysInMonth = (date: Date): DayInfo[] => {
    const days: DayInfo[] = []
    const year = date.getFullYear()
    const month = date.getMonth()

    // Primer día del mes
    const firstDay = new Date(year, month, 1)
    // Último día del mes
    const lastDay = new Date(year, month + 1, 0)

    // Días del mes anterior para completar la primera semana
    const firstDayOfWeek = firstDay.getDay()
    for (let i = firstDayOfWeek; i > 0; i--) {
      days.push({
        date: null,
        disabled: true,
        isToday: false,
      })
    }

    // Días del mes actual
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    const weeklySchedule = selectedWorkshop.scheduleConfig.weeklySchedule
    const dayMap: { [key: number]: keyof typeof weeklySchedule } = {
      0: 'sunday',
      1: 'monday',
      2: 'tuesday',
      3: 'wednesday',
      4: 'thursday',
      5: 'friday',
      6: 'saturday',
    }

    for (let i = 1; i <= lastDay.getDate(); i++) {
      const currentDate = new Date(year, month, i)
      const dayOfWeek = currentDate.getDay()
      const dayName = dayMap[dayOfWeek]

      // Verificar si el día está en el horario del taller
      const isDayAvailable = weeklySchedule[dayName] !== undefined

      // Verificar si el día es hoy o futuro
      const isPastDay = currentDate < today

      days.push({
        date: currentDate,
        disabled: isPastDay || !isDayAvailable,
        isToday: currentDate.toDateString() === today.toDateString(),
      })
    }

    return days
  }

  const handleDateSelect = async (dayInfo: DayInfo) => {
    if (!dayInfo.date || dayInfo.disabled) return

    // Inmediatamente establecer la fecha seleccionada y avanzar al siguiente paso
    setSelectedDate(dayInfo.date)
    setCurrentStep(4)

    try {
      setLoading(true)
      setError(null)

      const dateStr = dayInfo.date.toISOString().split('T')[0]

      // Obtener los slots disponibles usando la acción correcta
      const result = await actions.getAvailableWorkshopSlots({
        workshopId: selectedWorkshop._id,
        date: dateStr,
        serviceTypeId: selectedServiceType._id,
      })

      if (!result.data?.success || !result.data) {
        toast.error(result.error?.message || "No se pudieron obtener los horarios disponibles")
        setError(result.error?.message || "No se pudieron obtener los horarios disponibles")
        // Si hay un error, volver al calendario
        setCurrentStep(3)
        return
      }

      // Establecer los slots disponibles
      setAvailableSlots(result.data.data)
    } catch (error: any) {
      toast.error(error.message || "Error al obtener horarios disponibles")
      setError(error.message || "Error al obtener horarios disponibles")
      // Si hay un error, volver al calendario
      setCurrentStep(3)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-4">
      <Button variant="ghost" onClick={goBack}>
        <ArrowLeft className="mr-2" /> Regresar
      </Button>

      <h2 className="text-xl font-bold text-center">Selecciona una fecha</h2>

      <div className="flex items-center justify-between mb-4">
        <Button variant="outline" onClick={goToPreviousMonth}>
          <ChevronLeft className="h-4 w-4" />
        </Button>

        <h3 className="text-lg font-medium">
          {currentMonth.toLocaleDateString('es-ES', { month: 'long', year: 'numeric' })}
        </h3>

        <Button variant="outline" onClick={goToNextMonth}>
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>

      <div className="grid grid-cols-7 gap-1 text-center">
        {DAYS.map(day => (
          <div key={day} className="font-medium text-sm py-2">
            {day}
          </div>
        ))}
      </div>

      <div className="grid grid-cols-7 gap-1">
        {getDaysInMonth(currentMonth).map((day, index) => (
          <Button
            key={index}
            variant={selectedDate?.toDateString() === day.date?.toDateString() ? "default" : "ghost"}
            disabled={day.disabled}
            className={`h-12 ${day.isToday ? 'border-2 border-primary' : ''} 
                       ${!day.date ? 'invisible' : ''} 
                       ${day.disabled ? 'opacity-50' : ''}`}
            onClick={() => handleDateSelect(day)}
          >
            {day.date?.getDate()}
          </Button>
        ))}
      </div>
    </div>
  )
}




