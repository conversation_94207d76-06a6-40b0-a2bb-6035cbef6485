"use client"

import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft, Calendar, Clock, MapPin, Car, User, Gauge, ReceiptText } from "lucide-react"
import { useAppointment } from "./AppointmentContext"
import { DateTime } from "luxon"

export default function ExistingAppointment({ setError }: { setError: (error: string | null) => void }) {
  const { appointmentInfo, plates } = useAppointment()

  if (!appointmentInfo || !appointmentInfo.appointment) {
    return null
  }

  const { appointment } = appointmentInfo

  const formatDate = (dateStr: string) => {
    return DateTime.fromISO(dateStr)
      .setLocale('es')
      .toFormat("EEEE dd 'de' LLLL 'de' yyyy")
  }

  const formatTime = (dateStr: string) => {
    return DateTime.fromISO(dateStr)
      .toFormat('hh:mm a')
      .toUpperCase()
  }

  const handleReschedule = () => {
    // Redirigir a la página de reagendamiento
    window.location.href = `/talleres/reagendar`
  }

  return (
    <div className="space-y-6">
      <div className="bg-yellow-50 p-4 rounded-lg">
        <h2 className="text-lg font-semibold text-yellow-800">Ya tienes una cita agendada</h2>
        <p className="text-yellow-700 mt-1">
          Este vehículo ya tiene una cita de mantenimiento agendada. Puedes ver los detalles a continuación o reagendarla.
        </p>
      </div>

      <div className="bg-gray-50 p-4 rounded-lg space-y-4 mt-4 text-left">
        <div className="flex items-start gap-3">
          <Calendar className="h-5 w-5 text-violet-700 mt-0.5" />
          <div>
            <p className="font-medium">Fecha</p>
            <p className="text-gray-600">{formatDate(appointment.startTime)}</p>
          </div>
        </div>

        <div className="flex items-start gap-3">
          <Clock className="h-5 w-5 text-violet-700 mt-0.5" />
          <div>
            <p className="font-medium">Hora</p>
            <p className="text-gray-600">{formatTime(appointment.startTime)}</p>
          </div>
        </div>

        {/* Show workshop name and google maps link */}

        <div className="flex items-start gap-3">
          <MapPin className="h-5 w-5 text-violet-700 mt-0.5" />
          <div>
            <p className="font-medium">Taller</p>
            <p className="text-gray-600">{appointment.workshop.name}</p>
            <a
              href={appointment.workshop.location.mapsLink}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-500 hover:underline"
            >
              Ver en Google Maps
            </a>
          </div>
        </div>

        {/* Show contract number {appointment.stockVehicle.contractNumber} */}
        <div className="flex items-start gap-3">
          <ReceiptText className="h-5 w-5 text-violet-700 mt-0.5" />
          <div>
            <p className="font-medium">Contrato</p>
            <p className="text-gray-600">{appointment.stockVehicle.contractNumber}</p>
          </div>
        </div>

        <div className="flex items-start gap-3">
          <Car className="h-5 w-5 text-violet-700 mt-0.5" />
          <div>
            <p className="font-medium">Vehículo</p>
            <p className="text-gray-600">
              {appointment.stockVehicle.brand} {appointment.stockVehicle.model} -
              Placas: {appointment.stockVehicle?.carPlates?.plates?.toUpperCase()}
            </p>
          </div>
        </div>

        <div className="flex items-start gap-3">
          <Gauge className="h-5 w-5 text-violet-700 mt-0.5" />
          <div>
            <p className="font-medium">Kilometraje Registrado</p>
            <p className="text-gray-600">{appointment.data.registeredKm} km</p>
          </div>
        </div>

        <div className="flex items-start gap-3">
          <Gauge className="h-5 w-5 text-violet-700 mt-0.5" />
          <div>
            <p className="font-medium">Kilometraje de mantenimiento</p>
            <p className="text-gray-600">{appointment.maintenanceKm} km</p>
          </div>
        </div>

        <div className="flex items-start gap-3">
          <User className="h-5 w-5 text-violet-700 mt-0.5" />
          <div>
            <p className="font-medium">Nombre del conductor</p>
            <p className="text-gray-600">
              {appointment.associate.firstName} {appointment.associate.lastName}
            </p>
          </div>
        </div>
      </div>

      <div className="bg-violet-50 p-4 rounded-lg mt-4">
        <h3 className="font-medium text-violet-800">Detalles del servicio</h3>
        <p className="text-violet-700 mt-1">
          {appointment.serviceType.name} - Mantenimiento #{appointment.data.maintenanceNumber}
        </p>
        <p className="text-sm text-violet-600 mt-1">
          Duración aproximada: {appointment.duration} minutos
        </p>
      </div>

      <div className="flex flex-col gap-3 mt-6">
        <Button
          className="w-full bg-violet-700 hover:bg-violet-800"
          onClick={handleReschedule}
        >
          Reagendar cita
        </Button>

        <Button
          variant="outline"
          className="w-full"
          onClick={() => window.location.reload()}
        >
          Volver al inicio
        </Button>
      </div>
    </div>
  )
}