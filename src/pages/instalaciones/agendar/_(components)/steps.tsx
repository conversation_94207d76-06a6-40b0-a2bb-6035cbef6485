"use client"

import { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { CheckCircle2, ArrowLeft, Camera, Upload, ChevronLeft, ChevronRight, Trash2 } from "lucide-react"
import toast from "react-hot-toast"
import { DateTime } from "luxon"
import { actions } from 'astro:actions'
import type { Address, AppointmentRequest } from '@/types/appointment'
import { AddressForm } from './AddressForm'
import { TimeSlotSelector } from './TimeSlotSelector'
import { AppointmentSummary } from './AppointmentSummary'
// import { useSearchParams } from 'react-router-dom'; // Comentado porque no se usa


interface VehicleInfo {
  // plates: string
  carPlates: {
    plates: string
  }
  _id: string
  brand: string
  model: string
  vin: string
  driver: {
    _id: string
    name: string
    email: string
    phone: string
    address: string
  }
  appointment?: {
    _id: string
    status: 'scheduled' | 'installed'
    startTime: string
    // time: string
    // address: string
    address: Address;
    neighborhoodId: string;
  }
  neighborhoods: Array<{
    _id: string
    name: string
    scheduleConfig: {
      weeklySchedule: WeeklySchedule
      installationDuration: number // duración en minutos
    }
  }>
}

interface WeeklySchedule {
  monday?: TimeRange;
  tuesday?: TimeRange;
  wednesday?: TimeRange;
  thursday?: TimeRange;
  friday?: TimeRange;
  saturday?: TimeRange;
  sunday?: TimeRange;
  [key: string]: TimeRange | undefined;
}

interface TimeRange {
  start: string;
  end: string;
}

interface DayInfo {
  date: Date | null;
  disabled: boolean;
  isToday: boolean;
}

export default function InstallationSteps() {
  const [step, setStep] = useState<number>(1)
  const [plates, setPlates] = useState('')
  const [loading, setLoading] = useState(false)
  const [vehicleInfo, setVehicleInfo] = useState<VehicleInfo | null>(null)
  const [selectedNeighborhood, setSelectedNeighborhood] = useState('')
  const [issueDescription, setIssueDescription] = useState('')
  const [photoFiles, setPhotoFiles] = useState<File[]>([])
  const [currentMonth, setCurrentMonth] = useState<Date>(new Date())
  const [selectedDate, setSelectedDate] = useState<Date | null>(null)
  const [availableSlots, setAvailableSlots] = useState<string[]>([])
  const [selectedSlot, setSelectedSlot] = useState<string>('')
  const [address, setAddress] = useState<Address | null>(null)
  const [rescheduleSlots, setRescheduleSlots] = useState<string[]>([])
  const [rescheduleDate, setRescheduleDate] = useState<Date | null>(null)
  const [rescheduleSlot, setRescheduleSlot] = useState<string>('')

  // const [weeklySchedule, setWeeklySchedule] = useState<Record<string, boolean>>({});
  // const [loadingSchedule, setLoadingSchedule] = useState(false);
  // console.log('vehicleInfo', vehicleInfo)

  const DAYS: string[] = ['Dom', 'Lun', 'Mar', 'Mié', 'Jue', 'Vie', 'Sáb']
  const MONTHS: string[] = [
    'Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio',
    'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'
  ]

  const validatePlates = async () => {
    try {
      setLoading(true)
      const response = await actions.validatePlatesAndReturnData({ plates })
      // console.log('response validatePlatesAndReturnData', response);
      const data = response.data;
      // console.log('validation: ', response.error, !response.data, response.error || !response.data);
      if (response.error || !response.data) {
        toast.error('No se encontró ningún vehículo con esas placas')
        return
      }

      setVehicleInfo(data.vehicleInfo)

      if (data.vehicleInfo.appointment) {
        if (data.vehicleInfo.appointment.status === 'scheduled') {
          setStep(6) // Mostrar detalles de la cita
        } else if (data.vehicleInfo.appointment.status === 'installed') {
          setStep(8) // Ir directamente a la pantalla de verificación de instalación
        } else if (data.vehicleInfo.appointment.status === 'completed') {
          setStep(12) // Mostrar pantalla de instalación completada
        }
      } else {
        setStep(2) // Selección de colonia
      }
    } catch (error) {
      toast.error('Error al validar las placas')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    const validatePlatesFromUrl = async () => {
      const urlParams = new URLSearchParams(window.location.search);
      const platesParam = urlParams.get('placas');

      if (!platesParam) return;

      try {
        setLoading(true);
        setPlates(platesParam);
        const response = await actions.validatePlatesAndReturnData({ plates: platesParam });
        const data = response.data;

        if (response.error || !response.data) {
          toast.error('No se encontró ningún vehículo con esas placas');
          return;
        }

        setVehicleInfo(data.vehicleInfo);

        if (data.vehicleInfo.appointment) {
          if (data.vehicleInfo.appointment.status === 'scheduled') {
            setStep(6);
          } else if (data.vehicleInfo.appointment.status === 'installed') {
            setStep(8);
          } else if (data.vehicleInfo.appointment.status === 'completed') {
            setStep(12);
          }
        } else {
          setStep(2);
        }

        // Eliminar el parámetro de placas de la URL
        urlParams.delete('placas');
        const newUrl = window.location.pathname + (urlParams.toString() ? `?${urlParams.toString()}` : '');
        window.history.replaceState({}, '', newUrl);

      } catch (error) {
        toast.error('Error al validar las placas');
      } finally {
        setLoading(false);
      }
    };

    validatePlatesFromUrl();
  }, []); // Solo se ejecuta una vez al montar el componente

  const goBack = () => {
    setStep(step - 1)
  }

  const getDaysInMonth = (date: Date): DayInfo[] => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const days: DayInfo[] = [];
    const firstDayIndex = firstDay.getDay();

    // Get selected neighborhood schedule
    const neighborhood = vehicleInfo?.appointment?.neighborhoodId
      ? vehicleInfo.neighborhoods.find(n => n._id === vehicleInfo.appointment?.neighborhoodId)
      : vehicleInfo?.neighborhoods.find(n => n._id === selectedNeighborhood);

    if (!neighborhood) {
      return days;
    }

    // Si no existe scheduleConfig o weeklySchedule, deshabilitar todos los días
    if (!neighborhood.scheduleConfig?.weeklySchedule) {
      // Fill in empty days
      for (let i = 0; i < firstDayIndex; i++) {
        days.push({ date: null, disabled: true, isToday: false });
      }

      // Fill in month days (all disabled)
      for (let i = 1; i <= lastDay.getDate(); i++) {
        const currentDate = new Date(year, month, i);
        const isToday = new Date().toDateString() === currentDate.toDateString();

        days.push({
          date: currentDate,
          disabled: true, // Todos los días deshabilitados
          isToday,
        });
      }

      return days;
    }

    const weeklySchedule = neighborhood.scheduleConfig.weeklySchedule || {};

    // Fill in empty days
    for (let i = 0; i < firstDayIndex; i++) {
      days.push({ date: null, disabled: true, isToday: false });
    }

    // Fill in month days
    for (let i = 1; i <= lastDay.getDate(); i++) {
      const currentDate = new Date(year, month, i);
      const isToday = new Date().toDateString() === currentDate.toDateString();
      const isPast = currentDate < new Date(new Date().setHours(0, 0, 0, 0));

      // Get day of week in Spanish
      const dayName = currentDate.toLocaleDateString('es-ES', { weekday: 'long' });

      // Map Spanish day names to weeklySchedule keys
      const dayMap: { [key: string]: string } = {
        'lunes': 'monday',
        'martes': 'tuesday',
        'miércoles': 'wednesday',
        'jueves': 'thursday',
        'viernes': 'friday',
        'sábado': 'saturday',
        'domingo': 'sunday'
      };

      const scheduleKey = dayMap[dayName];
      const isDayUnavailable = !weeklySchedule[scheduleKey];

      days.push({
        date: currentDate,
        disabled: isPast || isDayUnavailable,
        isToday,
      });
    }

    return days;
  };

  const handleDateSelect = async (dayInfo: DayInfo) => {
    if (!dayInfo.date || dayInfo.disabled) return;
    setSelectedDate(dayInfo.date);

    try {
      const dateStr = dayInfo.date.toISOString().split('T')[0];
      const neighborhood = vehicleInfo?.neighborhoods.find(n => n._id === selectedNeighborhood);
      // Usar el mock de datos como especificaste
      const response = await actions.getAvailableAppointments({
        neighborhoodId: neighborhood?._id ?? '',
        date: dateStr,
      });
      // console.log('response', response);
      if (response.error) {
        toast.error('No se encontraron horarios disponibles');
        return;
      }

      setAvailableSlots(response.data);
      setStep(4); // Paso para mostrar horarios disponibles
    } catch (error) {
      toast.error('Error al obtener horarios disponibles');
    }
  };

  const renderCalendar = () => (
    <div className="">
      <div className="flex items-center justify-between mb-4">
        <Button
          variant="outline"
          size="icon"
          onClick={() => {
            setCurrentMonth(prev => {
              const newMonth = new Date(prev);
              newMonth.setMonth(newMonth.getMonth() - 1);
              return newMonth;
            });
          }}
        >
          <ChevronLeft className="w-4 h-4" />
        </Button>
        <h2 className="text-lg font-semibold">
          {MONTHS[currentMonth.getMonth()]} {currentMonth.getFullYear()}
        </h2>
        <Button
          variant="outline"
          size="icon"
          onClick={() => {
            setCurrentMonth(prev => {
              const newMonth = new Date(prev);
              newMonth.setMonth(newMonth.getMonth() + 1);
              return newMonth;
            });
          }}
        >
          <ChevronRight className="w-4 h-4" />
        </Button>
      </div>

      <div className="grid grid-cols-7 gap-1 text-center">
        {DAYS.map((day) => (
          <div key={day} className="font-medium text-sm py-2">
            {day}
          </div>
        ))}
      </div>

      <div className="grid grid-cols-7 gap-1">
        {getDaysInMonth(currentMonth).map((day, index) => (
          <Button
            key={index}
            variant={selectedDate?.toDateString() === day.date?.toDateString() ? 'default' : 'ghost'}
            disabled={day.disabled}
            className={`h-12 ${day.isToday ? 'border-2 border-primary' : ''} 
                       ${!day.date ? 'invisible' : ''} 
                       ${day.disabled ? 'opacity-50' : ''}`}
            onClick={() => handleDateSelect(day)}
          >
            {day.date?.getDate()}
          </Button>
        ))}
      </div>
    </div>
  );

  const handleConfirmAppointment = async () => {
    if (!vehicleInfo || !selectedSlot) {
      toast.error('Por favor selecciona un horario');
      return;
    }

    try {
      setLoading(true);
      const obj = {
        stockId: vehicleInfo._id,
        associateId: vehicleInfo.driver._id,
        neighborhoodId: selectedNeighborhood,
        startTime: selectedSlot,
        address: address!,
        mls: true,
      }

      await actions.bookAppointment(obj);
      setStep(7);
    } catch (error) {
      toast.error('Error al agendar la cita');
    } finally {
      setLoading(false);
    }
  };

  const handleRescheduleDateSelect = async (dayInfo: DayInfo) => {
    if (!dayInfo.date || dayInfo.disabled) return;
    setRescheduleDate(dayInfo.date);

    try {
      const dateStr = dayInfo.date.toISOString().split('T')[0];
      const neighborhood = vehicleInfo?.neighborhoods.find(n => n._id === selectedNeighborhood);

      const response = await actions.getAvailableAppointments({
        neighborhoodId: neighborhood?._id ?? '',
        date: dateStr,
      });

      if (response.error) {
        toast.error('No se encontraron horarios disponibles');
        return;
      }

      setRescheduleSlots(response.data);
      setStep(14); // Paso para mostrar horarios disponibles para reagendar
    } catch (error) {
      toast.error('Error al obtener horarios disponibles');
    }
  };

  const handleRescheduleAppointment = async () => {
    if (!vehicleInfo?.appointment?._id || !rescheduleSlot) {
      toast.error('Por favor selecciona un horario');
      return;
    }

    try {
      setLoading(true);
      console.log('vehicleInfo.appointment._id', vehicleInfo.appointment._id);
      console.log('rescheduleSlot', rescheduleSlot);
      console.log('--------------------------------------------')
      const response = await actions.rescheduleInstallationAppointment({
        appointmentId: vehicleInfo.appointment._id,
        startTime: rescheduleSlot,
      });

      if (response.error) {
        toast.error(response.error.message);
        return;
      }

      toast.success('Cita reagendada exitosamente');
      setStep(16);
    } catch (error) {
      toast.error('Error al reagendar la cita');
    } finally {
      setLoading(false);
    }
  };

  const renderStep = () => {
    switch (step) {
      case 1:
        return (
          <div className="p-6 flex flex-col items-center h-full space-y-4 flex-1">
            <h1 className="text-2xl font-bold text-center">
              Instalación 🔌 de Cargador
            </h1>
            <p>
              Ingresa las placas de tu vehículo para continuar
            </p>

            <Input
              value={plates}
              onChange={(e) => {
                const value = e.target.value.toUpperCase().replace(/[^A-Z0-9]/g, '')
                setPlates(value)
              }}
              placeholder="Placa"
              className="mt-10"
            />

            <Button
              className="w-full mt-10 bg-violet-700 hover:bg-violet-800 text-white rounded-md"
              onClick={validatePlates}
              disabled={loading}
            >
              {loading ? 'Validando...' : 'Continuar'}
            </Button>
          </div>
        )

      case 2:
        return (
          <div className="p-6 space-y-4">
            <Button variant="ghost" onClick={goBack} className="mb-4">
              <ArrowLeft className="mr-2" /> Regresar
            </Button>

            <h2 className="text-xl font-bold text-center">Selecciona la colonia más cercana</h2>

            <Select
              value={selectedNeighborhood}
              onValueChange={setSelectedNeighborhood}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Selecciona una colonia" />
              </SelectTrigger>
              <SelectContent  >
                {vehicleInfo?.neighborhoods.map(n => (
                  <SelectItem key={n._id} value={n._id}>
                    {n.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Button
              className="w-full bg-violet-700 hover:bg-violet-800 text-white"
              onClick={() => setStep(3)}
              disabled={!selectedNeighborhood}
            >
              Continuar
            </Button>
          </div>
        )

      case 3:
        return (
          <div className="p-6 space-y-4">
            <Button variant="ghost" onClick={goBack} className="mb-4">
              <ArrowLeft className="mr-2" /> Regresar
            </Button>

            <h2 className="text-xl font-bold text-center">Selecciona una fecha</h2>
            {renderCalendar()}
          </div>
        )

      case 4:
        const selectedNeighborhoodData2 = vehicleInfo?.neighborhoods.find(
          n => n._id === selectedNeighborhood
        );

        return (
          <div className="p-6 space-y-4">
            <Button variant="ghost" onClick={goBack}>
              <ArrowLeft className="mr-2" /> Regresar
            </Button>
            <h2 className="text-xl font-bold text-center">Selecciona un horario</h2>
            <TimeSlotSelector
              slots={availableSlots}
              onSelect={(slot) => {
                setSelectedSlot(slot);
                setStep(5);
              }}
              installationDuration={selectedNeighborhoodData2?.scheduleConfig?.installationDuration ?? 180}
            />
          </div>
        );

      case 5:
        return (
          <div className="p-6 space-y-4">
            <Button variant="ghost" onClick={goBack}>
              <ArrowLeft className="mr-2" /> Regresar
            </Button>
            <h2 className="text-xl font-bold text-center">Ingresa tu dirección</h2>
            <AddressForm
              onSubmit={(addressData) => {
                setAddress(addressData);
                setStep(6);
              }}
              initialValues={address} // Pasamos los valores guardados
            />
          </div>
        );

      case 6:
        if (vehicleInfo?.appointment && vehicleInfo.appointment.status === 'scheduled') {
          const parsedDate = DateTime.fromISO(vehicleInfo.appointment.startTime);
          const selectedNeighborhoodData = vehicleInfo.neighborhoods.find(
            n => n._id === selectedNeighborhood
          );
          return (
            <>
              {/* SHOW DESCRIPTION ABOUT THAT THE APPOINTMENT IS ALREADY SCHEDULED AND BLA BLA */}
              <div className="p-6 space-y-4">

                <h2 className="text-xl font-bold text-center">Cita agendada</h2>
                <p className="text-center">
                  Tu cita ya ha sido agendada y puedes ver los detalles a continuación.
                </p>
              </div>
              <AppointmentSummary
                data={{
                  vehicleInfo: {
                    plates: vehicleInfo.carPlates.plates,
                    brand: vehicleInfo.brand,
                    model: vehicleInfo.model,
                    vin: vehicleInfo.vin,
                  },
                  customerInfo: {
                    name: vehicleInfo.driver.name,
                    email: vehicleInfo.driver.email,
                    phone: vehicleInfo.driver.phone,
                  },
                  appointmentInfo: {
                    // date: selectedDate.toISOString(),
                    // time: selectedSlot,
                    // neighborhoodId: selectedNeighborhood,
                    // address: address,
                    date: vehicleInfo.appointment.startTime,
                    time: vehicleInfo.appointment.startTime,
                    neighborhoodId: selectedNeighborhoodData?._id ?? '',
                    address: vehicleInfo.appointment.address,
                  },
                }}
                installationDuration={selectedNeighborhoodData?.scheduleConfig.installationDuration ?? 180}
                showConfirmButton={false}
              />
              <Button
                className="w-full bg-violet-700 mt-6"
                onClick={() => {
                  setStep(13)
                  setSelectedNeighborhood(vehicleInfo!.appointment!.neighborhoodId)
                }} // Ir al paso de reagendado
              >
                Reagendar Cita
              </Button>
            </>
          );
        }
        if (!vehicleInfo || !selectedDate || !selectedSlot || !address) {
          return null;
        }
        // check if appointment is scheduled and show details

        const selectedNeighborhoodData = vehicleInfo.neighborhoods.find(
          n => n._id === selectedNeighborhood
        );

        return (
          <div className="space-y-4">
            <Button variant="ghost" onClick={goBack}>
              <ArrowLeft className="mr-2" /> Regresar
            </Button>
            <h2 className="text-xl font-bold text-center">Confirma tu cita</h2>
            <AppointmentSummary
              data={{
                vehicleInfo: {
                  plates: vehicleInfo.carPlates.plates,
                  brand: vehicleInfo.brand,
                  model: vehicleInfo.model,
                  vin: vehicleInfo.vin,
                },
                customerInfo: {
                  name: vehicleInfo.driver.name,
                  email: vehicleInfo.driver.email,
                  phone: vehicleInfo.driver.phone,
                },
                appointmentInfo: {
                  date: selectedDate.toISOString(),
                  time: selectedSlot,
                  neighborhoodId: selectedNeighborhood,
                  address: address,
                },
              }}
              installationDuration={selectedNeighborhoodData?.scheduleConfig.installationDuration ?? 180}
              onConfirm={handleConfirmAppointment}
            />
          </div>
        );

      case 7:
        return (
          <div className="flex items-center justify-center">
            <div className="p-6 flex flex-col items-center max-w-md">
              <div className="w-24 h-24 rounded-full bg-violet-50 border-4 border-violet-500 flex items-center justify-center mb-6">
                <CheckCircle2 className="w-14 h-14 text-violet-500" />
              </div>
              <h2 className="text-2xl font-bold mb-4 text-center">¡Cita agendada con éxito!</h2>
              <p className="text-center text-gray-600 mb-8">
                Tu cita para instalación de E-Charger ha sido agendada correctamente.
                Recibirás un correo de confirmación con los detalles.
              </p>
              <Button
                className="w-full bg-violet-700 hover:bg-violet-800 text-white h-12"
                onClick={() => window.location.href = '/instalaciones/agendar'}
              >
                Volver al inicio
              </Button>
            </div>
          </div>
        );

      case 8: // Installation verification
        return (
          <div className="p-6 flex flex-col items-center space-y-6">
            <h2 className="text-xl font-bold text-center">
              {/* Was your charger installed successfully? */}
              ¿Tu cargador fue instalado correctamente?
            </h2>
            <p className="text-center text-gray-600">
              {/* Please upload photo as proof that charger has been installed. */}
              Por favor, sube una foto como prueba de que el cargador ha sido instalado.
            </p>
            <div className="w-32 h-32 bg-gray-100 rounded-lg flex items-center justify-center">
              <Camera className="w-16 h-16 text-gray-400" />
            </div>
            <div className="flex flex-col w-full gap-3">
              <Button
                className="w-full bg-violet-600 hover:bg-violet-700 text-white"
                onClick={() => setStep(9)}
              >
                Continuar
              </Button>
              <Button
                className="w-full bg-red-500 hover:bg-red-600 text-white"
                onClick={() => setStep(10)}
              >
                Reportar problema
              </Button>
            </div>
          </div>
        )

      case 9: // Photo upload
        return (
          <div className="p-6 space-y-4">
            <Button variant="ghost" onClick={goBack} className="mb-4">
              <ArrowLeft className="mr-2" /> Regresar
            </Button>

            <h2 className="text-xl font-bold text-center">
              Sube una foto de tu cargador instalado
            </h2>



            <div className="border-2 border-dashed rounded-lg p-8 text-center">
              <input
                type="file"
                accept="image/*"
                multiple
                onChange={(e) => {
                  const files = Array.from(e.target.files || []);
                  setPhotoFiles([...photoFiles, ...files]);
                }}
                className="hidden"
                id="photo-upload"
              />
              <label
                htmlFor="photo-upload"
                className="cursor-pointer flex flex-col items-center"
              >
                <Camera className="w-12 h-12 text-gray-400" />
                <span>Haz clic para subir foto</span>
              </label>
            </div>
            {/* Preview de imágenes */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4 max-w-2xl mx-auto">
              {photoFiles.map((file, index) => (
                <div key={index} className="relative aspect-square w-full max-w-[200px] mx-auto">
                  <img
                    src={URL.createObjectURL(file)}
                    alt={`Preview ${index + 1}`}
                    className="w-full h-full object-cover rounded-lg"
                  />
                  <button
                    onClick={() => {
                      const newFiles = photoFiles.filter((_, i) => i !== index);
                      setPhotoFiles(newFiles);
                    }}
                    className="absolute top-2 right-2 p-2 cursor-pointer bg-red-500 rounded-full text-white hover:bg-red-600"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              ))}
            </div>
            <Button
              className="w-full bg-violet-700"
              onClick={async () => {
                try {
                  // const result = await actions.uploadPhotos({
                  //   appointmentId: vehicleInfo!.appointment!._id,
                  //   photos: photoFiles,
                  // });

                  const formData = new FormData();
                  formData.append('appointmentId', vehicleInfo!.appointment!._id);
                  photoFiles.forEach((photo) => {
                    formData.append('photos', photo);
                  });
                  const result = await actions.uploadPhotos(formData);


                  // console.log('result', result);
                  if (result.data) {
                    setStep(11);
                    toast.success('Fotos subidas exitosamente');
                  }

                } catch (error) {
                  toast.error('Ha ocurrido un error al subir las fotos');
                  console.log('error', error);
                }
              }}
              disabled={!photoFiles.length}
            >
              Guardar
            </Button>
          </div>
        )

      case 10: // Report issue
        return (
          <div className="p-6 space-y-4">
            <Button variant="ghost" onClick={() => setStep(8)} className="mb-4">
              <ArrowLeft className="mr-2" /> Regresar
            </Button>

            <h2 className="text-xl font-bold text-center">Reportar Problema</h2>
            <p className="text-center text-sm text-gray-600">
              Lamentamos que hayas tenido un problema con tu instalación.
              {/* Por favor describe el problema y nos pondremos en contacto contigo. */}
              Por favor contactanos en el siguiente link para resolver tu problema.
            </p>
            <Button
              className="w-full bg-green-500 hover:bg-green-600 text-white flex items-center justify-center gap-2"
              onClick={() => window.open('https://wa.link/ocnsoporte', '_blank')}
            >
              <svg className="w-6 h-6" viewBox="0 0 24 24" fill="currentColor">
                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413z" />
              </svg>
              Contactar por WhatsApp
            </Button>
            {/* <Textarea
              value={issueDescription}
              onChange={(e) => setIssueDescription(e.target.value)}
              placeholder="Describe el problema aquí..."
              className="min-h-[150px]"
            /> */}
            {/* <Button
              className="w-full bg-violet-700"
              onClick={() => setStep(11)}
              disabled={!issueDescription.trim()}
            >
              Enviar
            </Button> */}
          </div>
        )

      case 11: // Success report
        return (
          <div className="p-6 flex flex-col items-center">
            <div className="w-24 h-24 rounded-full border-4 border-green-500 flex items-center justify-center mb-6">
              <CheckCircle2 className="w-14 h-14 text-green-500" />
            </div>
            <h2 className="text-xl font-bold mb-2 text-center">
              Problema reportado exitosamente
            </h2>
            <p className="text-center mb-8">
              Nos pondremos en contacto contigo pronto
            </p>
            <Button
              className="w-full bg-violet-700"
              onClick={() => window.location.href = '/instalaciones/agendar'}
            >
              Cerrar
            </Button>
          </div>
        )

      case 12: // Installation completed
        return (
          <div className="p-6 flex flex-col items-center">
            <div className="w-24 h-24 rounded-full border-4 border-green-500 flex items-center justify-center mb-6">
              <CheckCircle2 className="w-14 h-14 text-green-500" />
            </div>
            <h2 className="text-xl font-bold mb-4 text-center">
              ¡Instalación Completada!
            </h2>
            <div className="text-center space-y-2 mb-8">
              <p className="text-gray-600">
                Tu cargador ha sido instalado y verificado exitosamente.
              </p>
              <p className="text-gray-600">
                Ya puedes comenzar a utilizar tu cargador eléctrico.
              </p>
              <p className="text-gray-600">
                Si tienes alguna duda o necesitas asistencia, no dudes en contactarnos.
              </p>
            </div>
            <Button
              className="w-full bg-violet-700"
              onClick={() => window.location.href = '/instalaciones/agendar'}
            >
              Cerrar
            </Button>
          </div>
        )

      case 13:
        const neighborhoodForReschedule = vehicleInfo?.appointment?.neighborhoodId
          ? vehicleInfo.neighborhoods.find(n => n._id === vehicleInfo.appointment?.neighborhoodId)
          : null;

        if (!neighborhoodForReschedule) {
          toast.error('No se encontró la información del vecindario');
          return null;
        }

        return (
          <div className="p-6 space-y-4">
            <Button variant="ghost" onClick={() => setStep(6)}>
              <ArrowLeft className="mr-2" /> Regresar
            </Button>
            <h2 className="text-xl font-bold text-center">Selecciona una nueva fecha</h2>
            <div className="mt-6">
              <div className="flex items-center justify-between mb-4">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => {
                    setCurrentMonth(prev => {
                      const newMonth = new Date(prev);
                      newMonth.setMonth(newMonth.getMonth() - 1);
                      return newMonth;
                    });
                  }}
                >
                  <ChevronLeft className="w-4 h-4" />
                </Button>
                <h2 className="text-lg font-semibold">
                  {MONTHS[currentMonth.getMonth()]} {currentMonth.getFullYear()}
                </h2>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => {
                    setCurrentMonth(prev => {
                      const newMonth = new Date(prev);
                      newMonth.setMonth(newMonth.getMonth() + 1);
                      return newMonth;
                    });
                  }}
                >
                  <ChevronRight className="w-4 h-4" />
                </Button>
              </div>

              <div className="grid grid-cols-7 gap-1 text-center">
                {DAYS.map((day) => (
                  <div key={day} className="font-medium text-sm py-2">
                    {day}
                  </div>
                ))}
              </div>

              <div className="grid grid-cols-7 gap-1">
                {getDaysInMonth(currentMonth).map((day, index) => (
                  <Button
                    key={index}
                    variant={rescheduleDate?.toDateString() === day.date?.toDateString() ? 'default' : 'ghost'}
                    disabled={day.disabled}
                    className={`h-12 ${day.isToday ? 'border-2 border-primary' : ''} 
                              ${!day.date ? 'invisible' : ''} 
                              ${day.disabled ? 'opacity-50' : ''}`}
                    onClick={() => handleRescheduleDateSelect(day)}
                  >
                    {day.date?.getDate()}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        );

      case 14: // Selección de nuevo horario
        return (
          <div className="p-6 space-y-4">
            <Button variant="ghost" onClick={() => setStep(13)}>
              <ArrowLeft className="mr-2" /> Regresar
            </Button>
            <h2 className="text-xl font-bold text-center">Selecciona nuevo horario</h2>
            <TimeSlotSelector
              slots={rescheduleSlots}
              onSelect={(slot) => {
                setRescheduleSlot(slot);
                setStep(15); // Ahora va al paso de confirmación
              }}
              installationDuration={180}
            />
          </div>
        );

      case 15:
        const selectedNeighborhoodDataReschedule = vehicleInfo?.neighborhoods.find(
          n => n._id === vehicleInfo.appointment?.neighborhoodId
        );

        if (!vehicleInfo || !rescheduleDate || !rescheduleSlot) {
          return null;
        }

        return (
          <>
            {
              loading && (
                <div className="fixed inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center z-50">
                  <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-violet-700"></div>
                </div>
              )
            }
            <div className="space-y-4">
              <Button variant="ghost" onClick={() => setStep(14)}>
                <ArrowLeft className="mr-2" /> Regresar
              </Button>
              <h2 className="text-xl font-bold text-center">Confirma tu nueva cita</h2>
              <AppointmentSummary
                data={{
                  vehicleInfo: {
                    plates: vehicleInfo.carPlates.plates,
                    brand: vehicleInfo.brand,
                    model: vehicleInfo.model,
                    vin: vehicleInfo.vin,
                  },
                  customerInfo: {
                    name: vehicleInfo.driver.name,
                    email: vehicleInfo.driver.email,
                    phone: vehicleInfo.driver.phone,
                  },
                  appointmentInfo: {
                    date: rescheduleDate.toISOString(),
                    time: rescheduleSlot,
                    // neighborhoodId: vehicleInfo.appointment?.neighborhoodId,
                    // address: vehicleInfo.appointment?.address,
                    neighborhoodId: selectedNeighborhoodDataReschedule?._id ?? '',
                    address: vehicleInfo.appointment!.address,
                  },
                }}
                installationDuration={selectedNeighborhoodDataReschedule?.scheduleConfig.installationDuration ?? 180}
                onConfirm={handleRescheduleAppointment}
                confirmText="Confirmar Reagendado"
              />
            </div>
          </>

        );

      case 16:
        return (
          <div className="p-6 flex flex-col items-center">
            <div className="w-24 h-24 rounded-full border-4 border-green-500 flex items-center justify-center mb-6">
              <CheckCircle2 className="w-14 h-14 text-green-500" />
            </div>
            <h2 className="text-xl font-bold mb-2 text-center">
              ¡Cita reagendada exitosamente!
            </h2>
            <p className="text-center text-gray-600 mb-8">
              Tu cita ha sido reagendada correctamente. Te esperamos en la nueva fecha y hora seleccionada.
            </p>
            <Button
              className="w-full bg-violet-700"
              onClick={() => window.location.href = '/instalaciones/agendar'}
            >
              Cerrar
            </Button>
          </div>
        );

      default:
        return null
    }
  }

  return (
    <div className={step !== 7 ? "" : undefined}>
      {loading && (
        <div className="fixed inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-violet-700"></div>
        </div>
      )}
      {renderStep()}
    </div>
  );
}
























