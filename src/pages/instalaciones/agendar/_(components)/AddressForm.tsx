import { useState } from 'react';
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import type { Address } from "@/types/appointment";

interface AddressFormProps {
  onSubmit: (address: Address) => void;
  initialValues?: Address | null;
}

export function AddressForm({ onSubmit, initialValues }: AddressFormProps) {
  const [formData, setFormData] = useState<Partial<Address>>(initialValues || {});
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleNumberInput = (e: React.ChangeEvent<HTMLInputElement>, field: string) => {
    const value = e.target.value.replace(/\D/g, '');
    setFormData(prev => ({ ...prev, [field]: value }));

    // Validación específica para código postal
    if (field === 'zipCode') {
      if (value.length !== 5) {
        setErrors(prev => ({
          ...prev,
          zipCode: 'El código postal debe tener 5 dígitos'
        }));
      } else {
        setErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors.zipCode;
          return newErrors;
        });
      }
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validación final antes de enviar
    if (formData.zipCode?.length !== 5) {
      setErrors(prev => ({
        ...prev,
        zipCode: 'El código postal debe tener 5 dígitos'
      }));
      return;
    }

    onSubmit(formData as Address);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <Input
        name="street"
        placeholder="Calle"
        required
        value={formData.street || ''}
        onChange={e => setFormData(prev => ({ ...prev, street: e.target.value }))}
      />
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Input
            name="exteriorNumber"
            placeholder="Número exterior"
            required
            value={formData.exteriorNumber || ''}
            onChange={e => handleNumberInput(e, 'exteriorNumber')}
          />
        </div>
        <div>
          <Input
            name="interiorNumber"
            placeholder="Número interior (opcional)"
            value={formData.interiorNumber || ''}
            onChange={e => handleNumberInput(e, 'interiorNumber')}
          />
        </div>
      </div>
      <Input
        name="colony"
        placeholder="Colonia"
        required
        value={formData.colony || ''}
        onChange={e => setFormData(prev => ({ ...prev, colony: e.target.value }))}
      />
      <div>
        <Input
          name="zipCode"
          placeholder="Código postal"
          required
          maxLength={5}
          value={formData.zipCode || ''}
          onChange={e => handleNumberInput(e, 'zipCode')}
        />
        {errors.zipCode && (
          <span className="text-sm text-red-500 mt-1">{errors.zipCode}</span>
        )}
      </div>
      <Textarea
        name="references"
        placeholder="Referencias"
        required
        value={formData.references || ''}
        onChange={e => setFormData(prev => ({ ...prev, references: e.target.value }))}
      />
      <Button
        type="submit"
        className="w-full bg-violet-700 hover:bg-violet-800 text-white"
        disabled={Object.keys(errors).length > 0}
      >
        Continuar
      </Button>
    </form>
  );
}

