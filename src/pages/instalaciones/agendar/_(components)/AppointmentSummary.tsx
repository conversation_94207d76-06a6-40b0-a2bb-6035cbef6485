import { Button } from "@/components/ui/button";
import type { AppointmentRequest } from "@/types/appointment";
import { DateTime } from "luxon";

type AppointmentSummaryBaseProps = {
  data: AppointmentRequest;
  installationDuration: number; // Añadimos esta prop
  confirmText?: string;
}
// merge type, if showConfirmButton is true, then onConfirm is required
// otherwise, onConfirm is optional

type AppointmentSummaryWithConfirm = AppointmentSummaryBaseProps & {
  onConfirm: () => void;
  showConfirmButton: true;
}

type AppointmentSummaryWithoutConfirm = AppointmentSummaryBaseProps & {
  onConfirm?: () => void;
  showConfirmButton?: false;
}

type AppointmentSummaryProps = AppointmentSummaryWithConfirm | AppointmentSummaryWithoutConfirm;


export function AppointmentSummary({ data, installationDuration, onConfirm, confirmText, showConfirmButton = true }: AppointmentSummaryProps) {
  const formatDate = (dateStr: string) => {
    return DateTime.fromISO(dateStr)
      .setLocale('es')
      .toFormat("dd 'de' LLLL 'de' yyyy");
  };

  const formatTimeRange = (timeStr: string) => {
    const start = DateTime.fromISO(timeStr);
    const end = start.plus({ minutes: installationDuration });
    return `${start.toFormat('hh:mm a').toLowerCase()} - ${end.toFormat('hh:mm a').toLowerCase()}`;
  };

  return (
    <div className="space-y-6 max-w-xl mx-auto">
      <div className="space-y-4 bg-gray-50 p-4 rounded-lg">
        <h3 className="font-semibold text-lg border-b pb-2">Información del vehículo</h3>
        <div className="grid grid-cols-2 gap-y-2">
          <span className="text-gray-600">Placas:</span>
          <span className="font-medium">{data.vehicleInfo.plates}</span>
          <span className="text-gray-600">Marca:</span>
          <span className="font-medium">{data.vehicleInfo.brand}</span>
          <span className="text-gray-600">Modelo:</span>
          <span className="font-medium">{data.vehicleInfo.model}</span>
          <span className="text-gray-600">VIN:</span>
          <span className="font-medium">{data.vehicleInfo.vin}</span>
        </div>
      </div>

      <div className="space-y-4 bg-gray-50 p-4 rounded-lg overflow-x-auto">
        <h3 className="font-semibold text-lg border-b pb-2">Información del cliente</h3>
        <div className="grid grid-cols-2 gap-y-2">
          <span className="text-gray-600">Nombre:</span>
          <span className="font-medium">{data.customerInfo.name}</span>
          <span className="text-gray-600">Email:</span>
          <span className="font-medium">{data.customerInfo.email}</span>
          <span className="text-gray-600">Teléfono:</span>
          <span className="font-medium">{data.customerInfo.phone}</span>
        </div>
      </div>

      <div className="space-y-4 bg-gray-50 p-4 rounded-lg">
        <h3 className="font-semibold text-lg border-b pb-2">Detalles de la cita</h3>
        <div className="grid grid-cols-2 gap-y-2">
          <span className="text-gray-600">Fecha:</span>
          <span className="font-medium">{formatDate(data.appointmentInfo.date)}</span>
          <span className="text-gray-600">Horario:</span>
          <span className="font-medium">{formatTimeRange(data.appointmentInfo.time)}</span>
          <span className="text-gray-600">Duración:</span>
          <span className="font-medium">{installationDuration / 60} horas</span>
        </div>
      </div>

      <div className="space-y-4 bg-gray-50 p-4 rounded-lg">
        <h3 className="font-semibold text-lg border-b pb-2">Dirección de instalación</h3>
        <div className="grid grid-cols-2 gap-y-2">
          <span className="text-gray-600">Calle:</span>
          <span className="font-medium">{data.appointmentInfo.address.street}</span>
          <span className="text-gray-600">Número exterior:</span>
          <span className="font-medium">{data.appointmentInfo.address.exteriorNumber}</span>
          {data.appointmentInfo.address.interiorNumber && (
            <>
              <span className="text-gray-600">Número interior:</span>
              <span className="font-medium">{data.appointmentInfo.address.interiorNumber}</span>
            </>
          )}
          <span className="text-gray-600">Colonia:</span>
          <span className="font-medium">{data.appointmentInfo.address.colony}</span>
          <span className="text-gray-600">CP:</span>
          <span className="font-medium">{data.appointmentInfo.address.zipCode}</span>
          <span className="text-gray-600">Referencias:</span>
          <span className="font-medium">{data.appointmentInfo.address.references}</span>
        </div>
      </div>

      {showConfirmButton && (
        <Button
          onClick={onConfirm}
          className="w-full bg-violet-700 hover:bg-violet-800 text-white h-12 text-lg"
        >
          {confirmText ?? 'Confirmar cita'}
        </Button>
      )}
    </div>
  );
}

