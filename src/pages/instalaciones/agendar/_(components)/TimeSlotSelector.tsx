import { Button } from "@/components/ui/button";
import { Clock } from "lucide-react";
import { DateTime } from "luxon";

interface TimeSlotSelectorProps {
  slots: string[];
  onSelect: (slot: string) => void;
  installationDuration: number; // duración en minutos
}

export function TimeSlotSelector({ slots, onSelect, installationDuration }: TimeSlotSelectorProps) {
  const formatTimeRange = (startTime: string) => {
    const start = DateTime.fromISO(startTime);
    const end = start.plus({ minutes: installationDuration });

    const startFormatted = start.toFormat('hh:mm a').toLowerCase();
    const endFormatted = end.toFormat('hh:mm a').toLowerCase();

    return `${startFormatted} - ${endFormatted}`;
  };

  return (
    <div className="flex flex-col gap-3">
      {slots.map((slot) => (
        <Button
          key={slot}
          variant="outline"
          onClick={() => onSelect(slot)}
          className="p-4 flex items-center justify-between w-full"
        >
          <div className="flex items-center gap-2">
            <Clock className="w-4 h-4" />
            <span>{formatTimeRange(slot)}</span>
          </div>
          <span className="text-sm text-gray-500">
            Duración: {installationDuration / 60} horas
          </span>
        </Button>
      ))}
    </div>
  );
}
