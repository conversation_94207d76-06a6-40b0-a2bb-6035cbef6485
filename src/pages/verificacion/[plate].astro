---
import Layout from '@/layouts/Layout.astro';
import VerificationForm from './_(components)/VerificationForm';

const { plate } = Astro.params;

if (!plate) {
  return Astro.redirect('/404');
}
---

<Layout title="Confirmación de Verificación">
  <main class="min-h-screen bg-white p-4 flex items-center justify-center">
    <div class="max-w-md w-full mx-auto h-full">
      <div class="h-full rounded-md overflow-hidden">
        <div class="p-4 text-center space-y-4">
          <VerificationForm client:load plate={plate} />
        </div>
      </div>
    </div>
  </main>
</Layout>