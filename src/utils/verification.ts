export type HologramType = '00' | '0' | '1' | '2';

export interface VerificationSchedule {
  nextVerificationDate: Date;
  isExempt: boolean;
  exemptUntil?: Date;
  verificationMonths: string[];
}

export function getVerificationMonthsByPlate(plate: string): string[] {
  // Find the last numeric digit in the plate
  const lastDigit = plate.match(/\d(?=\D*$)/)?.[0] || '6'; // Default to 6 if no digit found
  
  switch (lastDigit) {
    case '5':
    case '6':
      return ['Enero', 'Febrero', 'Julio', 'Agosto'];
    case '7':
    case '8':
      return ['Febrero', 'Marzo', 'Agosto', 'Septiembre'];
    case '3':
    case '4':
      return ['Mar<PERSON>', 'Abril', 'Septiembre', 'Octubre'];
    case '1':
    case '2':
      return ['Abril', 'Mayo', 'Octubre', 'Novi<PERSON>bre'];
    case '9':
    case '0':
      return ['<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Diciembre'];
    default:
      // For non-numeric endings (like letters), default to Jan-Feb, Jul-Aug
      return ['Enero', 'Febrero', '<PERSON>', 'Agosto'];
  }
}

export function getVerificationMonthNumbers(plate: string): number[] {
  // Find the last numeric digit in the plate
  const lastDigit = plate.match(/\d(?=\D*$)/)?.[0] || '6'; // Default to 6 if no digit found
  
  switch (lastDigit) {
    case '5':
    case '6':
      return [1, 2, 7, 8]; // Jan-Feb, Jul-Aug
    case '7':
    case '8':
      return [2, 3, 8, 9]; // Feb-Mar, Aug-Sep
    case '3':
    case '4':
      return [3, 4, 9, 10]; // Mar-Apr, Sep-Oct
    case '1':
    case '2':
      return [4, 5, 10, 11]; // Apr-May, Oct-Nov
    case '9':
    case '0':
      return [5, 6, 11, 12]; // May-Jun, Nov-Dec
    default:
      // For non-numeric endings (like letters), default to Jan-Feb, Jul-Aug
      return [1, 2, 7, 8];
  }
}

export function calculateNextVerificationDate(
  plate: string,
  hologramType: HologramType,
  currentDate: Date = new Date(),
  registrationDate?: Date,
  isHybridElectric?: boolean
): VerificationSchedule {
  const verificationMonths = getVerificationMonthNumbers(plate);
  
  // Hybrid/Electric: Check if exempt for 8 years from registration (PRIORITY)
  if (isHybridElectric && registrationDate) {
    const eightYearsFromRegistration = new Date(registrationDate);
    eightYearsFromRegistration.setFullYear(eightYearsFromRegistration.getFullYear() + 8);
    
    if (currentDate < eightYearsFromRegistration) {
      // Set to last day of the month for "verify before" logic
      const exemptMonth = eightYearsFromRegistration.getMonth() + 1;
      const exemptYear = eightYearsFromRegistration.getFullYear();
      const nextVerificationDate = new Date(exemptYear, exemptMonth, 0);
      
      return {
        nextVerificationDate,
        isExempt: true,
        exemptUntil: eightYearsFromRegistration,
        verificationMonths: getVerificationMonthsByPlate(plate)
      };
    }
  }
  
  // Hologram 00: Exempt for 2 years, then next verification period (last day)
  if (hologramType === '00') {
    const exemptUntil = new Date(currentDate);
    exemptUntil.setFullYear(exemptUntil.getFullYear() + 2);
    
    // Find the next verification month after exemption ends
    const exemptMonth = exemptUntil.getMonth() + 1;
    const exemptYear = exemptUntil.getFullYear();
    
    // Find next available verification month after exemption
    const nextAvailableMonth = verificationMonths.find(month => month > exemptMonth);
    let nextMonth: number;
    let nextYear = exemptYear;
    
    if (nextAvailableMonth) {
      nextMonth = nextAvailableMonth;
    } else {
      // Wrap to next year, take first month
      nextMonth = verificationMonths[0];
      nextYear = exemptYear + 1;
    }
    
    // Set to last day of the verification month
    const nextVerificationDate = new Date(nextYear, nextMonth, 0); // Day 0 = last day of previous month, so nextMonth gives us last day of nextMonth-1
    
    return {
      nextVerificationDate,
      isExempt: true,
      exemptUntil: exemptUntil,
      verificationMonths: getVerificationMonthsByPlate(plate)
    };
  }
  
  // Holograms 0, 1, 2: Every 6 months
  const currentMonth = currentDate.getMonth() + 1; // 1-based month
  const currentYear = currentDate.getFullYear();
  
  // Group verification months into periods: [period1, period2]
  // For plate ending in 6: [1,2] and [7,8] (Jan-Feb, Jul-Aug)
  const period1 = [verificationMonths[0], verificationMonths[1]];
  const period2 = [verificationMonths[2], verificationMonths[3]];
  
  let nextMonth: number;
  let nextYear = currentYear;
  
  // Check if we're currently in a verification period
  const isInPeriod1 = period1.includes(currentMonth);
  const isInPeriod2 = period2.includes(currentMonth);
  
  if (isInPeriod1) {
    // Currently in period 1, next verification in last month of period 2
    if (period2[1] > currentMonth) {
      // Period 2 is later this year, use last month of period 2
      nextMonth = period2[1];
    } else {
      // Period 2 is early next year, use last month of period 2
      nextMonth = period2[1];
      nextYear = currentYear + 1;
    }
  } else if (isInPeriod2) {
    // Currently in period 2, next verification in last month of period 1 of next year
    nextMonth = period1[1]; // Last month of period 1
    nextYear = currentYear + 1;
  } else {
    // Not in any period, go to last month of first period of next year
    nextMonth = period1[1]; // Last month of period 1
    nextYear = currentYear + 1;
  }
  
  // Set to last day of the verification month
  const nextVerificationDate = new Date(nextYear, nextMonth, 0); // Day 0 = last day of previous month
  
  return {
    nextVerificationDate,
    isExempt: false,
    verificationMonths: getVerificationMonthsByPlate(plate)
  };
}

export function getHologramColor(plate: string): string {
  // Find the last numeric digit in the plate
  const lastDigit = plate.match(/\d(?=\D*$)/)?.[0] || '6'; // Default to 6 if no digit found
  
  switch (lastDigit) {
    case '5':
    case '6':
      return 'Amarillo';
    case '7':
    case '8':
      return 'Rosa';
    case '3':
    case '4':
      return 'Rojo';
    case '1':
    case '2':
      return 'Verde';
    case '9':
    case '0':
      return 'Azul';
    default:
      // For non-numeric endings (like letters), default to Yellow
      return 'Amarillo';
  }
}

export function shouldShowVerificationReminder(
  registrationDate: Date,
  currentDate: Date = new Date()
): boolean {
  const sevenYearsFromRegistration = new Date(registrationDate);
  sevenYearsFromRegistration.setFullYear(sevenYearsFromRegistration.getFullYear() + 7);
  
  return currentDate >= sevenYearsFromRegistration;
}