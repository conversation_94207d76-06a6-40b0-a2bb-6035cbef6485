export interface Address {
  street: string;
  exteriorNumber: string;
  interiorNumber?: string;
  colony: string;
  zipCode: string;
  references: string;
}

export interface AppointmentRequest {
  vehicleInfo: {
    plates: string;
    brand: string;
    model: string;
    vin: string;
  };
  customerInfo: {
    name: string;
    email: string;
    phone: string;
  };
  appointmentInfo: {
    date: string;
    time: string;
    neighborhoodId: string;
    address: Address;
  };
}